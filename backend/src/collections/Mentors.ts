import type { CollectionConfig } from 'payload'

export const Mentors: CollectionConfig = {
  slug: 'mentors',
  admin: {
    useAsTitle: 'name',
  },
  access: {
    read: () => true,
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
    },
    {
      name: 'title',
      type: 'text',
      required: true,
    },
    {
      name: 'bio',
      type: 'textarea',
    },
    {
      name: 'image',
      type: 'upload',
      relationTo: 'media',
    },
    {
      name: 'company',
      type: 'text',
    },
    {
      name: 'experience',
      type: 'number',
      label: 'Years of Experience',
    },
    {
      name: 'specialization',
      type: 'array',
      fields: [
        {
          name: 'area',
          type: 'text',
        },
      ],
    },
    {
      name: 'linkedinUrl',
      type: 'text',
      label: 'LinkedIn URL',
    },
    {
      name: 'rating',
      type: 'number',
      min: 0,
      max: 5,
    },
    {
      name: 'totalSessions',
      type: 'number',
      label: 'Total Sessions Conducted',
    },
    {
      name: 'hourlyRate',
      type: 'number',
      label: 'Hourly Rate (₹)',
    },
    {
      name: 'isActive',
      type: 'checkbox',
      defaultValue: true,
    },
    {
      name: 'isFeatured',
      type: 'checkbox',
      defaultValue: false,
    },
  ],
}
