import type { CollectionConfig } from 'payload'

export const Companies: CollectionConfig = {
  slug: 'companies',
  admin: {
    useAsTitle: 'name',
  },
  access: {
    read: () => true,
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
    },
    {
      name: 'logo',
      type: 'upload',
      relationTo: 'media',
      required: true,
    },
    {
      name: 'website',
      type: 'text',
    },
    {
      name: 'industry',
      type: 'select',
      options: [
        { label: 'Consulting', value: 'consulting' },
        { label: 'Big 4', value: 'big4' },
        { label: 'Investment Banking', value: 'investment-banking' },
        { label: 'Technology', value: 'technology' },
        { label: 'Finance', value: 'finance' },
        { label: 'Audit', value: 'audit' },
        { label: 'Tax', value: 'tax' },
        { label: 'Other', value: 'other' },
      ],
    },
    {
      name: 'isHiringPartner',
      type: 'checkbox',
      defaultValue: false,
    },
    {
      name: 'displayOrder',
      type: 'number',
      defaultValue: 0,
    },
  ],
}
