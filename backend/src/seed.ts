import { getPayload } from 'payload'
import config from './payload.config'

const seed = async () => {
  const payload = await getPayload({ config })

  console.log('Seeding blog posts...')

  const blogPosts = [
    {
      title: 'Top CA Firms in Agra for Articleship',
      slug: 'top-ca-firms-in-agra-for-articleship',
      excerpt: 'Discover the top CA Firms in Agra. Gain hands-on experience in auditing and taxation to build a strong foundation for your CA career.',
      content: {
        root: {
          children: [
            {
              children: [
                {
                  detail: 0,
                  format: 0,
                  mode: 'normal',
                  style: '',
                  text: 'Discover the top CA Firms in Agra. Gain hands-on experience in auditing and taxation to build a strong foundation for your CA career.',
                  type: 'text',
                  version: 1,
                },
              ],
              direction: 'ltr',
              format: '',
              indent: 0,
              type: 'paragraph',
              version: 1,
            },
          ],
          direction: 'ltr',
          format: '',
          indent: 0,
          type: 'root',
          version: 1,
        },
      },
      author: 'CA Monk Team',
      category: 'general',
      tags: ['Blog', 'CA Student', 'General'],
      isPublished: true,
      isFeatured: false,
      publishedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), // 5 days ago
    },
    {
      title: 'Top 10 Essential Interview Questions for Internal Audit',
      slug: 'top-10-essential-interview-questions-for-internal-audit',
      excerpt: 'Top 10 interview questions for internal audit with expert tips to help you prepare and succeed in your next audit job interview.',
      content: {
        root: {
          children: [
            {
              children: [
                {
                  detail: 0,
                  format: 0,
                  mode: 'normal',
                  style: '',
                  text: 'Top 10 interview questions for internal audit with expert tips to help you prepare and succeed in your next audit job interview.',
                  type: 'text',
                  version: 1,
                },
              ],
              direction: 'ltr',
              format: '',
              indent: 0,
              type: 'paragraph',
              version: 1,
            },
          ],
          direction: 'ltr',
          format: '',
          indent: 0,
          type: 'root',
          version: 1,
        },
      },
      author: 'CA Monk Team',
      category: 'general',
      tags: ['Blog', 'CA Student', 'General'],
      isPublished: true,
      isFeatured: false,
      publishedAt: new Date(Date.now() - 21 * 24 * 60 * 60 * 1000), // 3 weeks ago
    },
    {
      title: 'A Guide to ICAI Women Placement Drive 2025',
      slug: 'a-guide-to-icai-women-placement-drive-2025',
      excerpt: 'Looking for remote work as a female Chartered Accountant? Explore the ICAI Women Placement Drive 2025 & Apply now!',
      content: {
        root: {
          children: [
            {
              children: [
                {
                  detail: 0,
                  format: 0,
                  mode: 'normal',
                  style: '',
                  text: 'Looking for remote work as a female Chartered Accountant? Explore the ICAI Women Placement Drive 2025 & Apply now!',
                  type: 'text',
                  version: 1,
                },
              ],
              direction: 'ltr',
              format: '',
              indent: 0,
              type: 'paragraph',
              version: 1,
            },
          ],
          direction: 'ltr',
          format: '',
          indent: 0,
          type: 'root',
          version: 1,
        },
      },
      author: 'CA Monk Team',
      category: 'placement',
      tags: ['Blog', 'CA Student', 'Placement'],
      isPublished: true,
      isFeatured: true,
      publishedAt: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000), // 2 months ago
    },
    {
      title: 'Excel Mastery for Chartered Accountants',
      slug: 'excel-mastery-for-chartered-accountants',
      excerpt: 'Master Excel skills that every CA needs. Learn advanced formulas, pivot tables, and financial modeling techniques.',
      content: {
        root: {
          children: [
            {
              children: [
                {
                  detail: 0,
                  format: 0,
                  mode: 'normal',
                  style: '',
                  text: 'Master Excel skills that every CA needs. Learn advanced formulas, pivot tables, and financial modeling techniques.',
                  type: 'text',
                  version: 1,
                },
              ],
              direction: 'ltr',
              format: '',
              indent: 0,
              type: 'paragraph',
              version: 1,
            },
          ],
          direction: 'ltr',
          format: '',
          indent: 0,
          type: 'root',
          version: 1,
        },
      },
      author: 'CA Shivam Palan',
      category: 'upskill',
      tags: ['Upskill', 'Excel', 'Finance'],
      isPublished: true,
      isFeatured: true,
      publishedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 1 week ago
    },
    {
      title: 'How to Crack Big 4 Interviews',
      slug: 'how-to-crack-big-4-interviews',
      excerpt: 'Complete guide to cracking interviews at Big 4 firms. Tips, strategies, and common questions answered.',
      content: {
        root: {
          children: [
            {
              children: [
                {
                  detail: 0,
                  format: 0,
                  mode: 'normal',
                  style: '',
                  text: 'Complete guide to cracking interviews at Big 4 firms. Tips, strategies, and common questions answered.',
                  type: 'text',
                  version: 1,
                },
              ],
              direction: 'ltr',
              format: '',
              indent: 0,
              type: 'paragraph',
              version: 1,
            },
          ],
          direction: 'ltr',
          format: '',
          indent: 0,
          type: 'root',
          version: 1,
        },
      },
      author: 'CA Monk Team',
      category: 'mentorship',
      tags: ['Mentorship', 'Interview', 'Big 4'],
      isPublished: true,
      isFeatured: false,
      publishedAt: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000), // 2 weeks ago
    },
  ]

  for (const post of blogPosts) {
    try {
      await payload.create({
        collection: 'blog-posts',
        data: post,
      })
      console.log(`Created blog post: ${post.title}`)
    } catch (error) {
      console.error(`Error creating blog post ${post.title}:`, error)
    }
  }

  console.log('Seeding completed!')
  process.exit(0)
}

seed().catch((error) => {
  console.error('Seeding failed:', error)
  process.exit(1)
})
