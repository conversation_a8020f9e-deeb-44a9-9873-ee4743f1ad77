/* tslint:disable */
/* eslint-disable */
/**
 * This file was automatically generated by Payload.
 * DO NOT MODIFY IT BY HAND. Instead, modify your source Payload config,
 * and re-run `payload generate:types` to regenerate this file.
 */

/**
 * Supported timezones in IANA format.
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "supportedTimezones".
 */
export type SupportedTimezones =
  | 'Pacific/Midway'
  | 'Pacific/Niue'
  | 'Pacific/Honolulu'
  | 'Pacific/Rarotonga'
  | 'America/Anchorage'
  | 'Pacific/Gambier'
  | 'America/Los_Angeles'
  | 'America/Tijuana'
  | 'America/Denver'
  | 'America/Phoenix'
  | 'America/Chicago'
  | 'America/Guatemala'
  | 'America/New_York'
  | 'America/Bogota'
  | 'America/Caracas'
  | 'America/Santiago'
  | 'America/Buenos_Aires'
  | 'America/Sao_Paulo'
  | 'Atlantic/South_Georgia'
  | 'Atlantic/Azores'
  | 'Atlantic/Cape_Verde'
  | 'Europe/London'
  | 'Europe/Berlin'
  | 'Africa/Lagos'
  | 'Europe/Athens'
  | 'Africa/Cairo'
  | 'Europe/Moscow'
  | 'Asia/Riyadh'
  | 'Asia/Dubai'
  | 'Asia/Baku'
  | 'Asia/Karachi'
  | 'Asia/Tashkent'
  | 'Asia/Calcutta'
  | 'Asia/Dhaka'
  | 'Asia/Almaty'
  | 'Asia/Jakarta'
  | 'Asia/Bangkok'
  | 'Asia/Shanghai'
  | 'Asia/Singapore'
  | 'Asia/Tokyo'
  | 'Asia/Seoul'
  | 'Australia/Brisbane'
  | 'Australia/Sydney'
  | 'Pacific/Guam'
  | 'Pacific/Noumea'
  | 'Pacific/Auckland'
  | 'Pacific/Fiji';

export interface Config {
  auth: {
    users: UserAuthOperations;
  };
  blocks: {};
  collections: {
    users: User;
    media: Media;
    courses: Course;
    mentors: Mentor;
    testimonials: Testimonial;
    'blog-posts': BlogPost;
    companies: Company;
    'payload-locked-documents': PayloadLockedDocument;
    'payload-preferences': PayloadPreference;
    'payload-migrations': PayloadMigration;
  };
  collectionsJoins: {};
  collectionsSelect: {
    users: UsersSelect<false> | UsersSelect<true>;
    media: MediaSelect<false> | MediaSelect<true>;
    courses: CoursesSelect<false> | CoursesSelect<true>;
    mentors: MentorsSelect<false> | MentorsSelect<true>;
    testimonials: TestimonialsSelect<false> | TestimonialsSelect<true>;
    'blog-posts': BlogPostsSelect<false> | BlogPostsSelect<true>;
    companies: CompaniesSelect<false> | CompaniesSelect<true>;
    'payload-locked-documents': PayloadLockedDocumentsSelect<false> | PayloadLockedDocumentsSelect<true>;
    'payload-preferences': PayloadPreferencesSelect<false> | PayloadPreferencesSelect<true>;
    'payload-migrations': PayloadMigrationsSelect<false> | PayloadMigrationsSelect<true>;
  };
  db: {
    defaultIDType: string;
  };
  globals: {};
  globalsSelect: {};
  locale: null;
  user: User & {
    collection: 'users';
  };
  jobs: {
    tasks: unknown;
    workflows: unknown;
  };
}
export interface UserAuthOperations {
  forgotPassword: {
    email: string;
    password: string;
  };
  login: {
    email: string;
    password: string;
  };
  registerFirstUser: {
    email: string;
    password: string;
  };
  unlock: {
    email: string;
    password: string;
  };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "users".
 */
export interface User {
  id: string;
  firstName: string;
  lastName: string;
  role: 'admin' | 'editor' | 'user';
  updatedAt: string;
  createdAt: string;
  email: string;
  resetPasswordToken?: string | null;
  resetPasswordExpiration?: string | null;
  salt?: string | null;
  hash?: string | null;
  loginAttempts?: number | null;
  lockUntil?: string | null;
  sessions?:
    | {
        id: string;
        createdAt?: string | null;
        expiresAt: string;
      }[]
    | null;
  password?: string | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "media".
 */
export interface Media {
  id: string;
  alt: string;
  updatedAt: string;
  createdAt: string;
  url?: string | null;
  thumbnailURL?: string | null;
  filename?: string | null;
  mimeType?: string | null;
  filesize?: number | null;
  width?: number | null;
  height?: number | null;
  focalX?: number | null;
  focalY?: number | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "courses".
 */
export interface Course {
  id: string;
  title: string;
  slug: string;
  description: string;
  category: 'interview-prep' | 'masterclass' | 'upskill' | 'mentorship' | 'free-courses' | 'placement-drive';
  price: number;
  originalPrice?: number | null;
  isFree?: boolean | null;
  isFeatured?: boolean | null;
  trustedBy?: number | null;
  image?: (string | null) | Media;
  instructor?: string | null;
  duration?: string | null;
  level?: ('beginner' | 'intermediate' | 'advanced') | null;
  tags?:
    | {
        tag?: string | null;
        id?: string | null;
      }[]
    | null;
  courseUrl?: string | null;
  status?: ('draft' | 'published' | 'archived') | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "mentors".
 */
export interface Mentor {
  id: string;
  name: string;
  title: string;
  bio?: string | null;
  image?: (string | null) | Media;
  company?: string | null;
  experience?: number | null;
  specialization?:
    | {
        area?: string | null;
        id?: string | null;
      }[]
    | null;
  linkedinUrl?: string | null;
  rating?: number | null;
  totalSessions?: number | null;
  hourlyRate?: number | null;
  isActive?: boolean | null;
  isFeatured?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "testimonials".
 */
export interface Testimonial {
  id: string;
  studentName: string;
  studentImage?: (string | null) | Media;
  testimonial: string;
  rating: number;
  course?: (string | null) | Course;
  studentTitle?: string | null;
  company?: string | null;
  isVerified?: boolean | null;
  isFeatured?: boolean | null;
  dateSubmitted?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "blog-posts".
 */
export interface BlogPost {
  id: string;
  title: string;
  slug: string;
  excerpt: string;
  content: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  };
  featuredImage?: (string | null) | Media;
  author: string;
  category: 'placement' | 'upskill' | 'ca-student' | 'mentorship' | 'general' | 'blog';
  tags?:
    | {
        tag?: string | null;
        id?: string | null;
      }[]
    | null;
  readTime?: number | null;
  isPublished?: boolean | null;
  isFeatured?: boolean | null;
  publishedAt?: string | null;
  metaTitle?: string | null;
  metaDescription?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "companies".
 */
export interface Company {
  id: string;
  name: string;
  logo: string | Media;
  website?: string | null;
  industry?:
    | ('consulting' | 'big4' | 'investment-banking' | 'technology' | 'finance' | 'audit' | 'tax' | 'other')
    | null;
  isHiringPartner?: boolean | null;
  displayOrder?: number | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-locked-documents".
 */
export interface PayloadLockedDocument {
  id: string;
  document?:
    | ({
        relationTo: 'users';
        value: string | User;
      } | null)
    | ({
        relationTo: 'media';
        value: string | Media;
      } | null)
    | ({
        relationTo: 'courses';
        value: string | Course;
      } | null)
    | ({
        relationTo: 'mentors';
        value: string | Mentor;
      } | null)
    | ({
        relationTo: 'testimonials';
        value: string | Testimonial;
      } | null)
    | ({
        relationTo: 'blog-posts';
        value: string | BlogPost;
      } | null)
    | ({
        relationTo: 'companies';
        value: string | Company;
      } | null);
  globalSlug?: string | null;
  user: {
    relationTo: 'users';
    value: string | User;
  };
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-preferences".
 */
export interface PayloadPreference {
  id: string;
  user: {
    relationTo: 'users';
    value: string | User;
  };
  key?: string | null;
  value?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-migrations".
 */
export interface PayloadMigration {
  id: string;
  name?: string | null;
  batch?: number | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "users_select".
 */
export interface UsersSelect<T extends boolean = true> {
  firstName?: T;
  lastName?: T;
  role?: T;
  updatedAt?: T;
  createdAt?: T;
  email?: T;
  resetPasswordToken?: T;
  resetPasswordExpiration?: T;
  salt?: T;
  hash?: T;
  loginAttempts?: T;
  lockUntil?: T;
  sessions?:
    | T
    | {
        id?: T;
        createdAt?: T;
        expiresAt?: T;
      };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "media_select".
 */
export interface MediaSelect<T extends boolean = true> {
  alt?: T;
  updatedAt?: T;
  createdAt?: T;
  url?: T;
  thumbnailURL?: T;
  filename?: T;
  mimeType?: T;
  filesize?: T;
  width?: T;
  height?: T;
  focalX?: T;
  focalY?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "courses_select".
 */
export interface CoursesSelect<T extends boolean = true> {
  title?: T;
  slug?: T;
  description?: T;
  category?: T;
  price?: T;
  originalPrice?: T;
  isFree?: T;
  isFeatured?: T;
  trustedBy?: T;
  image?: T;
  instructor?: T;
  duration?: T;
  level?: T;
  tags?:
    | T
    | {
        tag?: T;
        id?: T;
      };
  courseUrl?: T;
  status?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "mentors_select".
 */
export interface MentorsSelect<T extends boolean = true> {
  name?: T;
  title?: T;
  bio?: T;
  image?: T;
  company?: T;
  experience?: T;
  specialization?:
    | T
    | {
        area?: T;
        id?: T;
      };
  linkedinUrl?: T;
  rating?: T;
  totalSessions?: T;
  hourlyRate?: T;
  isActive?: T;
  isFeatured?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "testimonials_select".
 */
export interface TestimonialsSelect<T extends boolean = true> {
  studentName?: T;
  studentImage?: T;
  testimonial?: T;
  rating?: T;
  course?: T;
  studentTitle?: T;
  company?: T;
  isVerified?: T;
  isFeatured?: T;
  dateSubmitted?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "blog-posts_select".
 */
export interface BlogPostsSelect<T extends boolean = true> {
  title?: T;
  slug?: T;
  excerpt?: T;
  content?: T;
  featuredImage?: T;
  author?: T;
  category?: T;
  tags?:
    | T
    | {
        tag?: T;
        id?: T;
      };
  readTime?: T;
  isPublished?: T;
  isFeatured?: T;
  publishedAt?: T;
  metaTitle?: T;
  metaDescription?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "companies_select".
 */
export interface CompaniesSelect<T extends boolean = true> {
  name?: T;
  logo?: T;
  website?: T;
  industry?: T;
  isHiringPartner?: T;
  displayOrder?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-locked-documents_select".
 */
export interface PayloadLockedDocumentsSelect<T extends boolean = true> {
  document?: T;
  globalSlug?: T;
  user?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-preferences_select".
 */
export interface PayloadPreferencesSelect<T extends boolean = true> {
  user?: T;
  key?: T;
  value?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-migrations_select".
 */
export interface PayloadMigrationsSelect<T extends boolean = true> {
  name?: T;
  batch?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "auth".
 */
export interface Auth {
  [k: string]: unknown;
}


declare module 'payload' {
  export interface GeneratedTypes extends Config {}
}