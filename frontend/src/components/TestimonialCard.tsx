import Image from 'next/image'

interface TestimonialCardProps {
  studentName: string
  testimonial: string
  rating: number
  studentImage?: string
  studentTitle?: string
  company?: string
}

const TestimonialCard = ({ 
  studentName, 
  testimonial, 
  rating, 
  studentImage, 
  studentTitle, 
  company 
}: TestimonialCardProps) => {
  return (
    <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-300">
      {/* Rating */}
      <div className="flex items-center mb-4">
        <div className="flex items-center">
          {[...Array(5)].map((_, i) => (
            <svg
              key={i}
              className={`w-5 h-5 ${
                i < Math.floor(rating) ? 'text-yellow-400' : 'text-gray-300'
              }`}
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
            </svg>
          ))}
          <span className="ml-2 text-sm font-medium text-gray-900">{rating}</span>
        </div>
      </div>

      {/* Testimonial Text */}
      <p className="text-gray-700 mb-6 leading-relaxed">
        {testimonial}
      </p>

      {/* Student Info */}
      <div className="flex items-center">
        <div className="relative w-12 h-12 rounded-full overflow-hidden bg-gray-200 mr-4">
          {studentImage ? (
            <Image
              src={studentImage}
              alt={studentName}
              fill
              className="object-cover"
            />
          ) : (
            <div className="flex items-center justify-center h-full bg-gradient-to-br from-blue-500 to-purple-600">
              <span className="text-white text-sm font-semibold">
                {studentName.split(' ').map(n => n[0]).join('')}
              </span>
            </div>
          )}
        </div>
        
        <div>
          <h4 className="font-semibold text-gray-900">{studentName}</h4>
          {studentTitle && (
            <p className="text-sm text-gray-600">{studentTitle}</p>
          )}
          {company && (
            <p className="text-xs text-gray-500">{company}</p>
          )}
        </div>
      </div>
    </div>
  )
}

export default TestimonialCard
