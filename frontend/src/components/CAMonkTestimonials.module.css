/* Custom animations for CA MONK testimonials */

@keyframes slideInFromLeft {
  0% {
    transform: translateX(-100px);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInFromRight {
  0% {
    transform: translateX(100px);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fadeInScale {
  0% {
    transform: scale(0.8);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes pulseGlow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.8);
  }
}

@keyframes floatUp {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
}

@keyframes rotateIn {
  0% {
    transform: rotate(-180deg) scale(0.5);
    opacity: 0;
  }
  100% {
    transform: rotate(0deg) scale(1);
    opacity: 1;
  }
}

.testimonialCard {
  animation: fadeInScale 0.6s ease-out;
}

.testimonialCardExit {
  animation: fadeInScale 0.3s ease-in reverse;
}

.profileImage {
  animation: rotateIn 0.8s ease-out;
}

.profileImageHover:hover {
  animation: floatUp 2s ease-in-out infinite;
}

.navigationArrow {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.navigationArrow:hover {
  animation: pulseGlow 1.5s ease-in-out infinite;
}

.autoPlayIndicator {
  animation: pulseGlow 2s ease-in-out infinite;
}

/* Smooth transitions for all elements */
.smoothTransition {
  transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Custom easing for CA MONK style */
.camonkEasing {
  transition-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Loading state */
.loadingState {
  position: relative;
  overflow: hidden;
}

.loadingState::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Responsive animations */
@media (max-width: 768px) {
  .testimonialCard {
    animation-duration: 0.4s;
  }
  
  .profileImage {
    animation-duration: 0.6s;
  }
}

/* Enhanced entrance animations */
@keyframes slideInFromBottom {
  0% {
    transform: translateY(50px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes bounceIn {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes slideInFromTop {
  0% {
    transform: translateY(-50px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Progress bar animation */
@keyframes progressFill {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}

.progressBar {
  animation: progressFill 6s linear infinite;
}

/* Enhanced testimonial card animations */
.testimonialCardEnter {
  animation: bounceIn 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.testimonialCardExit {
  animation: slideInFromTop 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) reverse;
}

/* Staggered entrance for surrounding profiles */
.profileStagger1 {
  animation: slideInFromBottom 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.1s both;
}

.profileStagger2 {
  animation: slideInFromBottom 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.2s both;
}

.profileStagger3 {
  animation: slideInFromBottom 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.3s both;
}

.profileStagger4 {
  animation: slideInFromBottom 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.4s both;
}

.profileStagger5 {
  animation: slideInFromBottom 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.5s both;
}

.profileStagger6 {
  animation: slideInFromBottom 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.6s both;
}

/* Accessibility - Respect reduced motion preference */
@media (prefers-reduced-motion: reduce) {
  .testimonialCard,
  .testimonialCardEnter,
  .testimonialCardExit,
  .profileImage,
  .profileStagger1,
  .profileStagger2,
  .profileStagger3,
  .profileStagger4,
  .profileStagger5,
  .profileStagger6,
  .navigationArrow,
  .autoPlayIndicator,
  .smoothTransition,
  .progressBar,
  .loadingState::before {
    animation: none;
    transition: none;
  }

  .profileImageHover:hover {
    animation: none;
    transform: scale(1.05);
  }
}
