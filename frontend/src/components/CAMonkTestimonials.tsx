'use client';

import React, { useState, useEffect, useRef } from 'react';
import { ChevronLeft, ChevronRight, Star } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import styles from './CAMonkTestimonials.module.css';

// Testimonial data matching CA MONK style
const testimonials = [
  {
    id: 1,
    name: '<PERSON><PERSON><PERSON>',
    rating: 4.8,
    company: 'Deloitte',
    companyLogo: 'Deloitte',
    testimonial: 'This workshop provided me with a deep understanding of the actual interview processes, identified my weaknesses, and guided me on how to improve. It also offered a collaborative learning experience with fellow peers.',
    image: '/testimonial-1.jpg',
    gradient: 'from-purple-500 to-pink-600'
  },
  {
    id: 2,
    name: '<PERSON><PERSON><PERSON>',
    rating: 4.6,
    company: 'KPMG',
    companyLogo: 'KPMG',
    testimonial: 'It assists me in organizing my thoughts and structuring my answers effectively. Moreover, it has played a role in reducing my fear of public speaking to some extent.',
    image: '/testimonial-2.jpg',
    gradient: 'from-blue-600 to-purple-700'
  },
  {
    id: 3,
    name: '<PERSON><PERSON>',
    rating: 4.8,
    company: 'EY',
    companyLogo: 'EY',
    testimonial: 'The mentorship program was exceptional. My mentor guided me through every step of the interview process and helped me build confidence.',
    image: '/testimonial-3.jpg',
    gradient: 'from-green-500 to-blue-600'
  },
  {
    id: 4,
    name: 'Anita Patel',
    rating: 4.7,
    company: 'PwC',
    companyLogo: 'PwC',
    testimonial: 'The practical approach and real-world case studies helped me excel in my interviews and land my dream job.',
    image: '/testimonial-4.jpg',
    gradient: 'from-orange-500 to-red-600'
  },
  {
    id: 5,
    name: 'Vikram Singh',
    rating: 4.8,
    company: 'Goldman Sachs',
    companyLogo: 'Goldman Sachs',
    testimonial: 'Outstanding mentorship and guidance. The program exceeded my expectations and transformed my career prospects.',
    image: '/testimonial-5.jpg',
    gradient: 'from-teal-500 to-green-600'
  },
  {
    id: 6,
    name: 'Sneha Gupta',
    rating: 4.9,
    company: 'JP Morgan',
    companyLogo: 'JP Morgan',
    testimonial: 'The comprehensive curriculum and expert mentors made all the difference in my career journey.',
    image: '/testimonial-6.jpg',
    gradient: 'from-indigo-500 to-purple-600'
  },
  {
    id: 7,
    name: 'Arjun Mehta',
    rating: 4.6,
    company: 'Accenture',
    companyLogo: 'Accenture',
    testimonial: 'Practical skills and industry insights that directly contributed to my success in securing a top-tier position.',
    image: '/testimonial-7.jpg',
    gradient: 'from-pink-500 to-rose-600'
  }
];

interface CAMonkTestimonialsProps {
  className?: string;
}

export default function CAMonkTestimonials({ className = '' }: CAMonkTestimonialsProps) {
  const [currentTestimonial, setCurrentTestimonial] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const [fadeClass, setFadeClass] = useState('opacity-100 scale-100');
  const [progressWidth, setProgressWidth] = useState(0);
  const autoPlayRef = useRef<NodeJS.Timeout | null>(null);
  const progressRef = useRef<NodeJS.Timeout | null>(null);

  // Auto-play functionality with progress bar
  const startAutoPlay = () => {
    if (autoPlayRef.current) clearInterval(autoPlayRef.current);
    if (progressRef.current) clearInterval(progressRef.current);

    setProgressWidth(0);

    // Progress bar animation
    progressRef.current = setInterval(() => {
      setProgressWidth(prev => {
        if (prev >= 100) {
          return 0;
        }
        return prev + (100 / 60); // 60 steps for 6 seconds
      });
    }, 100);

    autoPlayRef.current = setInterval(() => {
      if (!isHovered && !isAnimating) {
        handleNext();
      }
    }, 6000); // 6 seconds interval
  };

  const stopAutoPlay = () => {
    if (autoPlayRef.current) {
      clearInterval(autoPlayRef.current);
      autoPlayRef.current = null;
    }
    if (progressRef.current) {
      clearInterval(progressRef.current);
      progressRef.current = null;
    }
    setProgressWidth(0);
  };

  // Initialize auto-play on mount
  useEffect(() => {
    startAutoPlay();
    return () => stopAutoPlay();
  }, [isHovered, isAnimating]);

  const handleNext = () => {
    if (isAnimating) return;
    setIsAnimating(true);
    setFadeClass('opacity-0 scale-95');

    setTimeout(() => {
      setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
      setFadeClass('opacity-100 scale-100');
      setTimeout(() => setIsAnimating(false), 100);
    }, 250);

    // Restart auto-play timer
    startAutoPlay();
  };

  const handlePrev = () => {
    if (isAnimating) return;
    setIsAnimating(true);
    setFadeClass('opacity-0 scale-95');

    setTimeout(() => {
      setCurrentTestimonial((prev) => (prev - 1 + testimonials.length) % testimonials.length);
      setFadeClass('opacity-100 scale-100');
      setTimeout(() => setIsAnimating(false), 100);
    }, 250);

    // Restart auto-play timer
    startAutoPlay();
  };

  const handleMouseEnter = () => {
    setIsHovered(true);
    stopAutoPlay();
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
    startAutoPlay();
  };

  const current = testimonials[currentTestimonial];

  // Get surrounding testimonials for the circular layout
  const getSurroundingTestimonials = () => {
    const surrounding = [];
    for (let i = 1; i <= 6; i++) {
      const index = (currentTestimonial + i) % testimonials.length;
      surrounding.push(testimonials[index]);
    }
    return surrounding;
  };

  const surroundingTestimonials = getSurroundingTestimonials();

  return (
    <section className={`py-20 bg-gray-50 relative overflow-hidden ${className}`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            What Our Students Have To Say...
          </h2>
        </div>

        {/* Main Testimonial Layout */}
        <div
          className="relative max-w-5xl mx-auto"
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
        >
          {/* Surrounding Profile Images */}
          <div className="relative w-full h-96 md:h-[500px]">
            {/* Top Left */}
            <div className={`absolute top-0 left-8 md:left-16 ${styles.profileStagger1}`}>
              <div className={`w-16 h-16 md:w-20 md:h-20 rounded-full overflow-hidden border-4 border-white shadow-lg hover:shadow-xl hover:scale-110 ${styles.smoothTransition} ${styles.profileImageHover} cursor-pointer group`}>
                <div className={`w-full h-full bg-gradient-to-br ${surroundingTestimonials[0]?.gradient || 'from-blue-500 to-purple-600'} flex items-center justify-center group-hover:scale-105 ${styles.smoothTransition}`}>
                  <span className="text-white text-sm font-semibold">
                    {surroundingTestimonials[0]?.name.split(' ').map(n => n[0]).join('')}
                  </span>
                </div>
              </div>
            </div>

            {/* Top Right */}
            <div className={`absolute top-0 right-8 md:right-16 ${styles.profileStagger2}`}>
              <div className={`w-16 h-16 md:w-20 md:h-20 rounded-full overflow-hidden border-4 border-white shadow-lg hover:shadow-xl hover:scale-110 ${styles.smoothTransition} ${styles.profileImageHover} cursor-pointer group`}>
                <div className={`w-full h-full bg-gradient-to-br ${surroundingTestimonials[1]?.gradient || 'from-green-500 to-blue-600'} flex items-center justify-center group-hover:scale-105 ${styles.smoothTransition}`}>
                  <span className="text-white text-sm font-semibold">
                    {surroundingTestimonials[1]?.name.split(' ').map(n => n[0]).join('')}
                  </span>
                </div>
              </div>
            </div>

            {/* Left */}
            <div className={`absolute top-1/2 left-0 transform -translate-y-1/2 ${styles.profileStagger3}`}>
              <div className={`w-16 h-16 md:w-20 md:h-20 rounded-full overflow-hidden border-4 border-white shadow-lg hover:shadow-xl hover:scale-110 ${styles.smoothTransition} ${styles.profileImageHover} cursor-pointer group`}>
                <div className={`w-full h-full bg-gradient-to-br ${surroundingTestimonials[2]?.gradient || 'from-purple-500 to-pink-600'} flex items-center justify-center group-hover:scale-105 ${styles.smoothTransition}`}>
                  <span className="text-white text-sm font-semibold">
                    {surroundingTestimonials[2]?.name.split(' ').map(n => n[0]).join('')}
                  </span>
                </div>
              </div>
            </div>

            {/* Right */}
            <div className={`absolute top-1/2 right-0 transform -translate-y-1/2 ${styles.profileStagger4}`}>
              <div className={`w-16 h-16 md:w-20 md:h-20 rounded-full overflow-hidden border-4 border-white shadow-lg hover:shadow-xl hover:scale-110 ${styles.smoothTransition} ${styles.profileImageHover} cursor-pointer group`}>
                <div className={`w-full h-full bg-gradient-to-br ${surroundingTestimonials[3]?.gradient || 'from-orange-500 to-red-600'} flex items-center justify-center group-hover:scale-105 ${styles.smoothTransition}`}>
                  <span className="text-white text-sm font-semibold">
                    {surroundingTestimonials[3]?.name.split(' ').map(n => n[0]).join('')}
                  </span>
                </div>
              </div>
            </div>

            {/* Bottom Left */}
            <div className={`absolute bottom-0 left-8 md:left-16 ${styles.profileStagger5}`}>
              <div className={`w-16 h-16 md:w-20 md:h-20 rounded-full overflow-hidden border-4 border-white shadow-lg hover:shadow-xl hover:scale-110 ${styles.smoothTransition} ${styles.profileImageHover} cursor-pointer group`}>
                <div className={`w-full h-full bg-gradient-to-br ${surroundingTestimonials[4]?.gradient || 'from-teal-500 to-green-600'} flex items-center justify-center group-hover:scale-105 ${styles.smoothTransition}`}>
                  <span className="text-white text-sm font-semibold">
                    {surroundingTestimonials[4]?.name.split(' ').map(n => n[0]).join('')}
                  </span>
                </div>
              </div>
            </div>

            {/* Bottom Right */}
            <div className={`absolute bottom-0 right-8 md:right-16 ${styles.profileStagger6}`}>
              <div className={`w-16 h-16 md:w-20 md:h-20 rounded-full overflow-hidden border-4 border-white shadow-lg hover:shadow-xl hover:scale-110 ${styles.smoothTransition} ${styles.profileImageHover} cursor-pointer group`}>
                <div className={`w-full h-full bg-gradient-to-br ${surroundingTestimonials[5]?.gradient || 'from-indigo-500 to-purple-600'} flex items-center justify-center group-hover:scale-105 ${styles.smoothTransition}`}>
                  <span className="text-white text-sm font-semibold">
                    {surroundingTestimonials[5]?.name.split(' ').map(n => n[0]).join('')}
                  </span>
                </div>
              </div>
            </div>

            {/* Central Testimonial */}
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-full max-w-md">
              <div className={`bg-white rounded-2xl shadow-xl p-6 md:p-8 text-center ${styles.smoothTransition} ${styles.camonkEasing} ${fadeClass} ${isAnimating ? 'pointer-events-none' : ''} ${isAnimating ? styles.loadingState : styles.testimonialCardEnter}`}>
                {/* Main Profile Image */}
                <div className={`w-24 h-24 md:w-32 md:h-32 rounded-full overflow-hidden border-4 border-white shadow-lg mx-auto mb-4 ${styles.profileImage} ${styles.profileImageHover} transition-transform duration-300 hover:scale-105`}>
                  <div className={`w-full h-full bg-gradient-to-br ${current.gradient || 'from-blue-600 to-purple-700'} flex items-center justify-center`}>
                    <span className="text-white text-xl md:text-2xl font-bold">
                      {current.name.split(' ').map(n => n[0]).join('')}
                    </span>
                  </div>
                </div>

                {/* Name */}
                <h3 className="text-xl md:text-2xl font-bold text-gray-900 mb-2 transition-all duration-300">
                  {current.name}
                </h3>

                {/* Rating */}
                <div className="flex items-center justify-center mb-2">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className={`w-4 h-4 transition-all duration-300 ${
                        i < Math.floor(current.rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'
                      }`}
                    />
                  ))}
                  <span className="ml-2 text-sm font-bold text-gray-700">{current.rating}</span>
                </div>

                {/* Company */}
                <div className="mb-4">
                  <span className="text-lg font-bold text-gray-800 transition-all duration-300">
                    {current.company}
                  </span>
                </div>

                {/* Testimonial */}
                <p className="text-gray-600 text-sm md:text-base leading-relaxed mb-6 transition-all duration-300">
                  {current.testimonial}
                </p>
              </div>
            </div>
          </div>

          {/* Navigation Arrows - Positioned Further Out */}
          <Button
            onClick={handlePrev}
            variant="outline"
            size="icon"
            className={`absolute -left-6 md:-left-12 top-1/2 transform -translate-y-1/2 w-12 h-12 md:w-14 md:h-14 rounded-full bg-blue-600 hover:bg-blue-700 hover:scale-110 text-white border-0 shadow-lg hover:shadow-xl z-20 ${styles.navigationArrow} ${styles.camonkEasing}`}
            disabled={isAnimating}
          >
            <ChevronLeft className="w-6 h-6 md:w-7 md:h-7" />
          </Button>

          <Button
            onClick={handleNext}
            variant="outline"
            size="icon"
            className={`absolute -right-6 md:-right-12 top-1/2 transform -translate-y-1/2 w-12 h-12 md:w-14 md:h-14 rounded-full bg-blue-600 hover:bg-blue-700 hover:scale-110 text-white border-0 shadow-lg hover:shadow-xl z-20 ${styles.navigationArrow} ${styles.camonkEasing}`}
            disabled={isAnimating}
          >
            <ChevronRight className="w-6 h-6 md:w-7 md:h-7" />
          </Button>
        </div>

        {/* Dots Indicator */}
        <div className="flex justify-center mt-8 space-x-2">
          {testimonials.map((_, index) => (
            <button
              key={index}
              onClick={() => {
                if (!isAnimating && index !== currentTestimonial) {
                  setIsAnimating(true);
                  setFadeClass('opacity-0 scale-95');

                  setTimeout(() => {
                    setCurrentTestimonial(index);
                    setFadeClass('opacity-100 scale-100');
                    setTimeout(() => setIsAnimating(false), 100);
                  }, 250);

                  // Restart auto-play timer
                  startAutoPlay();
                }
              }}
              className={`w-3 h-3 rounded-full transition-all duration-300 hover:scale-125 ${
                index === currentTestimonial
                  ? 'bg-blue-600 scale-125 shadow-lg'
                  : 'bg-gray-300 hover:bg-gray-400'
              }`}
              disabled={isAnimating}
            />
          ))}
        </div>

        {/* Auto-play Indicator with Progress Bar */}
        {!isHovered && (
          <div className="flex flex-col items-center mt-4 space-y-2">
            <div className="text-xs text-gray-500 flex items-center space-x-1">
              <div className={`w-2 h-2 bg-blue-600 rounded-full ${styles.autoPlayIndicator}`}></div>
              <span>Auto-playing</span>
            </div>
            <div className="w-32 h-1 bg-gray-200 rounded-full overflow-hidden">
              <div
                className="h-full bg-blue-600 rounded-full transition-all duration-100 ease-linear"
                style={{ width: `${progressWidth}%` }}
              ></div>
            </div>
          </div>
        )}

        {/* View All Button */}
        <div className="text-center mt-8">
          <Button variant="outline" className="font-semibold">
            View All
          </Button>
        </div>
      </div>
    </section>
  );
}
