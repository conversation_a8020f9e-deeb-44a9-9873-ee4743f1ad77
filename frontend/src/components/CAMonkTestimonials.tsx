'use client';

import React, { useState } from 'react';
import { ChevronLeft, ChevronRight, Star } from 'lucide-react';
import { Button } from '@/components/ui/button';

// Testimonial data matching CA MONK style
const testimonials = [
  {
    id: 1,
    name: '<PERSON><PERSON><PERSON>',
    rating: 4.6,
    company: 'KPMG',
    companyLogo: 'KPMG',
    testimonial: 'It assists me in organizing my thoughts and structuring my answers effectively. Moreover, it has played a role in reducing my fear of public speaking to some extent.',
    image: '/testimonial-1.jpg', // You'll need to add actual images
    position: 'center'
  },
  {
    id: 2,
    name: '<PERSON><PERSON>',
    rating: 4.8,
    company: 'Deloitte',
    companyLogo: 'Deloitte',
    testimonial: 'The mentorship program was exceptional. My mentor guided me through every step of the interview process.',
    image: '/testimonial-2.jpg',
    position: 'top-left'
  },
  {
    id: 3,
    name: '<PERSON><PERSON>',
    rating: 4.9,
    company: 'EY',
    companyLogo: 'EY',
    testimonial: '<PERSON>\'s courses are incredibly practical and industry-relevant. The Excel mastery course transformed my approach.',
    image: '/testimonial-3.jpg',
    position: 'top-right'
  },
  {
    id: 4,
    name: '<PERSON>',
    rating: 4.7,
    company: 'PwC',
    companyLogo: 'PwC',
    testimonial: 'The practical approach and real-world case studies helped me excel in my interviews.',
    image: '/testimonial-4.jpg',
    position: 'left'
  },
  {
    id: 5,
    name: 'Vikram Singh',
    rating: 4.8,
    company: 'Goldman Sachs',
    companyLogo: 'Goldman Sachs',
    testimonial: 'Outstanding mentorship and guidance. The program exceeded my expectations.',
    image: '/testimonial-5.jpg',
    position: 'right'
  },
  {
    id: 6,
    name: 'Sneha Gupta',
    rating: 4.9,
    company: 'JP Morgan',
    companyLogo: 'JP Morgan',
    testimonial: 'The comprehensive curriculum and expert mentors made all the difference in my career.',
    image: '/testimonial-6.jpg',
    position: 'bottom-left'
  },
  {
    id: 7,
    name: 'Arjun Mehta',
    rating: 4.6,
    company: 'Accenture',
    companyLogo: 'Accenture',
    testimonial: 'Practical skills and industry insights that directly contributed to my success.',
    image: '/testimonial-7.jpg',
    position: 'bottom-right'
  }
];

interface CAMonkTestimonialsProps {
  className?: string;
}

export default function CAMonkTestimonials({ className = '' }: CAMonkTestimonialsProps) {
  const [currentTestimonial, setCurrentTestimonial] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);

  const handleNext = () => {
    if (isAnimating) return;
    setIsAnimating(true);
    setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
    setTimeout(() => setIsAnimating(false), 500);
  };

  const handlePrev = () => {
    if (isAnimating) return;
    setIsAnimating(true);
    setCurrentTestimonial((prev) => (prev - 1 + testimonials.length) % testimonials.length);
    setTimeout(() => setIsAnimating(false), 500);
  };

  const current = testimonials[currentTestimonial];

  // Get surrounding testimonials for the circular layout
  const getSurroundingTestimonials = () => {
    const surrounding = [];
    for (let i = 1; i <= 6; i++) {
      const index = (currentTestimonial + i) % testimonials.length;
      surrounding.push(testimonials[index]);
    }
    return surrounding;
  };

  const surroundingTestimonials = getSurroundingTestimonials();

  return (
    <section className={`py-20 bg-gray-50 relative overflow-hidden ${className}`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            What Our Students Have To Say...
          </h2>
        </div>

        {/* Main Testimonial Layout */}
        <div className="relative max-w-4xl mx-auto">
          {/* Surrounding Profile Images */}
          <div className="relative w-full h-96 md:h-[500px]">
            {/* Top Left */}
            <div className="absolute top-0 left-8 md:left-16">
              <div className="w-16 h-16 md:w-20 md:h-20 rounded-full overflow-hidden border-4 border-white shadow-lg">
                <div className="w-full h-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                  <span className="text-white text-sm font-semibold">
                    {surroundingTestimonials[0]?.name.split(' ').map(n => n[0]).join('')}
                  </span>
                </div>
              </div>
            </div>

            {/* Top Right */}
            <div className="absolute top-0 right-8 md:right-16">
              <div className="w-16 h-16 md:w-20 md:h-20 rounded-full overflow-hidden border-4 border-white shadow-lg">
                <div className="w-full h-full bg-gradient-to-br from-green-500 to-blue-600 flex items-center justify-center">
                  <span className="text-white text-sm font-semibold">
                    {surroundingTestimonials[1]?.name.split(' ').map(n => n[0]).join('')}
                  </span>
                </div>
              </div>
            </div>

            {/* Left */}
            <div className="absolute top-1/2 left-0 transform -translate-y-1/2">
              <div className="w-16 h-16 md:w-20 md:h-20 rounded-full overflow-hidden border-4 border-white shadow-lg">
                <div className="w-full h-full bg-gradient-to-br from-purple-500 to-pink-600 flex items-center justify-center">
                  <span className="text-white text-sm font-semibold">
                    {surroundingTestimonials[2]?.name.split(' ').map(n => n[0]).join('')}
                  </span>
                </div>
              </div>
            </div>

            {/* Right */}
            <div className="absolute top-1/2 right-0 transform -translate-y-1/2">
              <div className="w-16 h-16 md:w-20 md:h-20 rounded-full overflow-hidden border-4 border-white shadow-lg">
                <div className="w-full h-full bg-gradient-to-br from-orange-500 to-red-600 flex items-center justify-center">
                  <span className="text-white text-sm font-semibold">
                    {surroundingTestimonials[3]?.name.split(' ').map(n => n[0]).join('')}
                  </span>
                </div>
              </div>
            </div>

            {/* Bottom Left */}
            <div className="absolute bottom-0 left-8 md:left-16">
              <div className="w-16 h-16 md:w-20 md:h-20 rounded-full overflow-hidden border-4 border-white shadow-lg">
                <div className="w-full h-full bg-gradient-to-br from-teal-500 to-green-600 flex items-center justify-center">
                  <span className="text-white text-sm font-semibold">
                    {surroundingTestimonials[4]?.name.split(' ').map(n => n[0]).join('')}
                  </span>
                </div>
              </div>
            </div>

            {/* Bottom Right */}
            <div className="absolute bottom-0 right-8 md:right-16">
              <div className="w-16 h-16 md:w-20 md:h-20 rounded-full overflow-hidden border-4 border-white shadow-lg">
                <div className="w-full h-full bg-gradient-to-br from-indigo-500 to-purple-600 flex items-center justify-center">
                  <span className="text-white text-sm font-semibold">
                    {surroundingTestimonials[5]?.name.split(' ').map(n => n[0]).join('')}
                  </span>
                </div>
              </div>
            </div>

            {/* Central Testimonial */}
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-full max-w-md">
              <div className="bg-white rounded-2xl shadow-xl p-6 md:p-8 text-center">
                {/* Main Profile Image */}
                <div className="w-24 h-24 md:w-32 md:h-32 rounded-full overflow-hidden border-4 border-white shadow-lg mx-auto mb-4">
                  <div className="w-full h-full bg-gradient-to-br from-blue-600 to-purple-700 flex items-center justify-center">
                    <span className="text-white text-xl md:text-2xl font-bold">
                      {current.name.split(' ').map(n => n[0]).join('')}
                    </span>
                  </div>
                </div>

                {/* Name */}
                <h3 className="text-xl md:text-2xl font-bold text-gray-900 mb-2">{current.name}</h3>

                {/* Rating */}
                <div className="flex items-center justify-center mb-2">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className={`w-4 h-4 ${
                        i < Math.floor(current.rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'
                      }`}
                    />
                  ))}
                  <span className="ml-2 text-sm font-bold text-gray-700">{current.rating}</span>
                </div>

                {/* Company */}
                <div className="mb-4">
                  <span className="text-lg font-bold text-gray-800">{current.company}</span>
                </div>

                {/* Testimonial */}
                <p className="text-gray-600 text-sm md:text-base leading-relaxed mb-6">
                  {current.testimonial}
                </p>
              </div>
            </div>
          </div>

          {/* Navigation Arrows */}
          <Button
            onClick={handlePrev}
            variant="outline"
            size="icon"
            className="absolute left-4 top-1/2 transform -translate-y-1/2 w-12 h-12 rounded-full bg-blue-600 hover:bg-blue-700 text-white border-0 shadow-lg z-10"
          >
            <ChevronLeft className="w-6 h-6" />
          </Button>

          <Button
            onClick={handleNext}
            variant="outline"
            size="icon"
            className="absolute right-4 top-1/2 transform -translate-y-1/2 w-12 h-12 rounded-full bg-blue-600 hover:bg-blue-700 text-white border-0 shadow-lg z-10"
          >
            <ChevronRight className="w-6 h-6" />
          </Button>
        </div>

        {/* View All Button */}
        <div className="text-center mt-12">
          <Button variant="outline" className="font-semibold">
            View All
          </Button>
        </div>
      </div>
    </section>
  );
}
