'use client';

import React, { useState, useEffect } from 'react';

// Company logo data with enhanced designs
const companyLogos = [
  {
    id: 'jpmorgan',
    name: 'JP Morgan Chase & Co',
    component: () => (
      <div className="bg-white rounded-xl px-4 py-3 shadow-xl border border-gray-200 min-w-[140px] hover:shadow-2xl transition-all duration-300">
        <div className="text-center">
          <div className="text-xs font-bold text-slate-800 uppercase tracking-wider">JP MORGAN</div>
          <div className="text-xs text-slate-600 uppercase tracking-wider">CHASE & CO.</div>
        </div>
      </div>
    ),
    positions: [
      { top: '8%', left: '0%' },
      { top: '15%', right: '10%' },
      { top: '25%', left: '5%' },
      { top: '35%', right: '5%' }
    ]
  },
  {
    id: 'dell',
    name: 'Dell Technologies',
    component: () => (
      <div className="bg-white rounded-xl px-4 py-3 shadow-xl border border-gray-200 min-w-[140px] hover:shadow-2xl transition-all duration-300">
        <div className="text-center">
          <div className="text-sm font-bold text-blue-600 uppercase tracking-wider">DELL</div>
          <div className="text-xs text-slate-500 uppercase tracking-wider">TECHNOLOGIES</div>
        </div>
      </div>
    ),
    positions: [
      { top: '12%', right: '4%' },
      { top: '20%', left: '8%' },
      { top: '30%', right: '15%' },
      { top: '40%', left: '2%' }
    ]
  },
  {
    id: 'wipro',
    name: 'Wipro',
    component: () => (
      <div className="bg-white rounded-xl px-4 py-3 shadow-xl border border-gray-200 min-w-[100px] hover:shadow-2xl transition-all duration-300">
        <div className="text-center">
          <div className="text-sm font-bold text-orange-500 lowercase">wipro</div>
          <div className="w-6 h-0.5 bg-orange-500 mx-auto mt-1"></div>
        </div>
      </div>
    ),
    positions: [
      { top: '50%', left: '0%', transform: 'translateY(-50%)' },
      { top: '45%', right: '5%' },
      { top: '55%', left: '10%' },
      { top: '60%', right: '12%' }
    ]
  },
  {
    id: 'infosys',
    name: 'Infosys',
    component: () => (
      <div className="bg-white rounded-xl px-4 py-3 shadow-xl border border-gray-200 min-w-[100px] hover:shadow-2xl transition-all duration-300">
        <div className="text-center">
          <div className="text-sm font-bold text-blue-700">Infosys</div>
          <div className="text-xs text-slate-500 uppercase tracking-wide">LIMITED</div>
        </div>
      </div>
    ),
    positions: [
      { bottom: '24%', left: '4%' },
      { bottom: '30%', right: '8%' },
      { bottom: '20%', left: '12%' },
      { bottom: '35%', right: '3%' }
    ]
  },
  {
    id: 'accenture',
    name: 'Accenture',
    component: () => (
      <div className="bg-white rounded-xl px-4 py-3 shadow-xl border border-gray-200 min-w-[120px] hover:shadow-2xl transition-all duration-300">
        <div className="text-center">
          <div className="text-sm font-bold text-purple-600 lowercase">accenture</div>
          <div className="text-lg text-purple-600 leading-none font-bold">></div>
        </div>
      </div>
    ),
    positions: [
      { bottom: '16%', right: '8%' },
      { bottom: '25%', left: '6%' },
      { bottom: '18%', right: '15%' },
      { bottom: '28%', left: '0%' }
    ]
  }
];

interface CompanyLogosProps {
  className?: string;
}

export default function CompanyLogos({ className = '' }: CompanyLogosProps) {
  const [currentLogoIndex, setCurrentLogoIndex] = useState(0);
  const [currentPositionIndex, setCurrentPositionIndex] = useState(0);
  const [isVisible, setIsVisible] = useState(true);
  const [animationPhase, setAnimationPhase] = useState<'fadeIn' | 'visible' | 'fadeOut'>('fadeIn');

  useEffect(() => {
    // Initial fade in
    setTimeout(() => {
      setAnimationPhase('visible');
    }, 100);

    const interval = setInterval(() => {
      setAnimationPhase('fadeOut');

      setTimeout(() => {
        setCurrentPositionIndex((prev) => {
          const nextPos = (prev + 1) % companyLogos[currentLogoIndex].positions.length;
          if (nextPos === 0) {
            setCurrentLogoIndex((prevLogo) => (prevLogo + 1) % companyLogos.length);
          }
          return nextPos;
        });

        setTimeout(() => {
          setAnimationPhase('fadeIn');
          setTimeout(() => {
            setAnimationPhase('visible');
          }, 200);
        }, 100);
      }, 400); // Fade out duration
    }, 3000); // Change every 3 seconds

    return () => clearInterval(interval);
  }, [currentLogoIndex]);

  const currentLogo = companyLogos[currentLogoIndex];
  const currentPosition = currentLogo.positions[currentPositionIndex];
  const LogoComponent = currentLogo.component;

  const getAnimationClasses = () => {
    switch (animationPhase) {
      case 'fadeIn':
        return 'opacity-0 scale-90 translate-y-2';
      case 'visible':
        return 'opacity-100 scale-100 translate-y-0';
      case 'fadeOut':
        return 'opacity-0 scale-95 -translate-y-1';
      default:
        return 'opacity-100 scale-100 translate-y-0';
    }
  };

  return (
    <div className={`absolute inset-0 z-20 pointer-events-none hidden lg:block ${className}`}>
      <div
        className={`absolute transition-all duration-500 ease-out ${getAnimationClasses()}`}
        style={{
          top: currentPosition.top,
          left: currentPosition.left,
          right: currentPosition.right,
          bottom: currentPosition.bottom,
          transform: currentPosition.transform,
        }}
      >
        <LogoComponent />
      </div>
    </div>
  );
}
