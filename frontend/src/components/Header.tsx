'use client'

import { useState } from 'react'
import Link from 'next/link'

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  const navigationItems = [
    {
      title: 'Interview Prep',
      href: '/interview-prep',
      badge: 'LIVE Interview Prep',
      submenu: [
        { title: 'Interview Prep Workshop', subtitle: 'Crack Interviews @ Top Companies', href: '/courses/interview-prep-workshop' },
        { title: 'Mega Prep Bootcamp', subtitle: 'Ace ICAI Campus Placements', href: '/courses/mega-prep-bootcamp' },
        { title: 'Articleship Training Masterclass', subtitle: 'Land Articleship for your dream job', href: '/courses/articleship-training' },
        { title: 'Industrial Training Masterclass', subtitle: 'Free masterclass for CA Articles', href: '/courses/industrial-training' },
        { title: 'Free resources', subtitle: 'Resources to kickstart your interview prep', href: '/free-resources' },
        { title: 'Placement drive', subtitle: 'Get hired by top companies', href: '/placement-drive' },
        { title: 'Company Guides', subtitle: 'Interview process & prep simplified', href: '/company-guides' },
        { title: 'Aptitude tests', subtitle: 'Brush up your technical concepts', href: '/aptitude-tests' },
      ]
    },
    {
      title: 'Masterclass',
      href: '/masterclass',
      submenu: [
        { title: 'Statutory Audit', subtitle: 'Perform Stat Audit like BIG 4 Consultant', href: '/courses/statutory-audit' },
        { title: 'Internal Audit', subtitle: 'Optimize company process to profitability', href: '/courses/internal-audit' },
        { title: 'Finance', subtitle: 'Your entry into Finance domain', href: '/courses/finance' },
        { title: 'Indirect Taxation', subtitle: 'Become an Indirect Tax Expert', href: '/courses/indirect-taxation' },
        { title: 'Investment Banking', subtitle: 'Become a matchmaker between companies', href: '/courses/investment-banking' },
        { title: 'Management Consulting', subtitle: 'Help businesses make right decisions', href: '/courses/management-consulting' },
        { title: 'Financial Due Diligence', subtitle: 'Your Pathway to Become an FDD Expert', href: '/courses/financial-due-diligence' },
      ]
    },
    {
      title: 'UpSkill',
      href: '/upskill',
      submenu: [
        { title: 'Financial Modelling & Valuation', subtitle: 'Your gateway to Finance & IB', href: '/courses/financial-modelling' },
        { title: 'Excel Mastery', subtitle: 'Most important foundational skill to learn', href: '/courses/excel-mastery' },
        { title: 'Sap FICO', subtitle: 'Tool used by Top MNCs for Finance', href: '/courses/sap-fico' },
        { title: 'Alteryx', subtitle: 'Present data in insightful visual dashboard', href: '/courses/alteryx' },
        { title: 'Python for Finance', subtitle: '10X your productivity using Technology', href: '/courses/python-finance' },
        { title: 'Tableau', subtitle: 'Understand your data to draw insights', href: '/courses/tableau' },
        { title: 'Google Data Studio', subtitle: 'Drive business decisions through data', href: '/courses/google-data-studio' },
      ]
    },
    {
      title: 'Tools',
      href: '/tools',
      submenu: [
        { title: 'Interview Bot', subtitle: 'Practice Mock Interviews with AI Bot', href: '/tools/interview-bot' },
        { title: 'Resume Scorer', subtitle: 'Check if your resume will be shortlisted?', href: '/tools/resume-scorer' },
        { title: 'Salary Estimator', subtitle: 'Know the market compensation for your profile', href: '/tools/salary-estimator' },
        { title: 'Articleship Scorer', subtitle: 'Check your articleship suitability', href: '/tools/articleship-scorer' },
      ]
    },
    {
      title: 'Hire From Us',
      href: '/hire-from-us',
    },
    {
      title: 'More',
      href: '#',
      submenu: [
        { title: 'WhatsApp Communities', subtitle: 'Get real-time updates on new opportunities', href: '/communities' },
        { title: 'Free Courses', subtitle: 'With recorded videos', href: '/free-courses' },
        { title: 'Events', subtitle: 'Learn with Leaders - LIVE', href: '/events' },
        { title: 'Jobs', subtitle: 'Exciting Finance Jobs @ Top Companies', href: '/jobs' },
        { title: 'Become a Mentor', subtitle: 'Teach, Mentor, Create Impact', href: '/become-mentor' },
        { title: 'Blogs', subtitle: 'Trending finance topics', href: '/blogs' },
        { title: 'Testimonials', subtitle: 'Why we do what we do', href: '/testimonials' },
        { title: 'Wall Of Fame', subtitle: 'Our Champions, Our pride', href: '/wall-of-fame' },
      ]
    }
  ]

  return (
    <>
      {/* Top Banner */}
      <div className="bg-blue-600 text-white text-center py-2 text-sm">
        <Link href="/courses/getting-interview-ready-workshop" className="hover:underline">
          Learn More
        </Link>
      </div>

      {/* Main Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo */}
            <div className="flex-shrink-0">
              <Link href="/" className="text-2xl font-bold text-blue-600">
                CA MONK
              </Link>
            </div>

            {/* Desktop Navigation */}
            <nav className="hidden lg:flex space-x-8">
              {navigationItems.map((item) => (
                <div key={item.title} className="relative group">
                  <Link
                    href={item.href}
                    className="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium flex items-center"
                  >
                    {item.title}
                    {item.badge && (
                      <span className="ml-2 bg-red-500 text-white text-xs px-2 py-1 rounded">
                        {item.badge}
                      </span>
                    )}
                    {item.submenu && (
                      <svg className="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    )}
                  </Link>

                  {/* Dropdown Menu */}
                  {item.submenu && (
                    <div className="absolute left-0 mt-2 w-80 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                      <div className="py-2">
                        {item.submenu.map((subItem) => (
                          <Link
                            key={subItem.title}
                            href={subItem.href}
                            className="block px-4 py-3 text-sm text-gray-700 hover:bg-gray-50"
                          >
                            <div className="font-medium">{subItem.title}</div>
                            {subItem.subtitle && (
                              <div className="text-xs text-gray-500 mt-1">{subItem.subtitle}</div>
                            )}
                          </Link>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </nav>

            {/* Right side buttons */}
            <div className="hidden lg:flex items-center space-x-4">
              <button className="text-gray-700 hover:text-blue-600">
                <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5-6m0 0h15M17 13v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6" />
                </svg>
              </button>
              <Link
                href="/login"
                className="bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700"
              >
                Login
              </Link>
            </div>

            {/* Mobile menu button */}
            <div className="lg:hidden">
              <button
                onClick={() => setIsMenuOpen(!isMenuOpen)}
                className="text-gray-700 hover:text-blue-600"
              >
                <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              </button>
            </div>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="lg:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t">
              {navigationItems.map((item) => (
                <Link
                  key={item.title}
                  href={item.href}
                  className="text-gray-700 hover:text-blue-600 block px-3 py-2 text-base font-medium"
                >
                  {item.title}
                </Link>
              ))}
              <Link
                href="/login"
                className="bg-blue-600 text-white block px-3 py-2 text-base font-medium rounded-md mt-4"
              >
                Login
              </Link>
            </div>
          </div>
        )}
      </header>
    </>
  )
}

export default Header
