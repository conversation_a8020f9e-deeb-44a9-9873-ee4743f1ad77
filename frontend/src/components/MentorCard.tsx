import Image from 'next/image'

interface MentorCardProps {
  name: string
  title: string
  image?: string
  company?: string
  rating?: number
  specialization?: string[]
}

const MentorCard = ({ 
  name, 
  title, 
  image, 
  company, 
  rating, 
  specialization 
}: MentorCardProps) => {
  return (
    <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-300 min-w-[280px]">
      {/* Mentor Image */}
      <div className="flex items-center mb-4">
        <div className="relative w-16 h-16 rounded-full overflow-hidden bg-gray-200 mr-4">
          {image ? (
            <Image
              src={image}
              alt={name}
              fill
              className="object-cover"
            />
          ) : (
            <div className="flex items-center justify-center h-full bg-gradient-to-br from-blue-500 to-purple-600">
              <span className="text-white text-lg font-semibold">
                {name.split(' ').map(n => n[0]).join('')}
              </span>
            </div>
          )}
        </div>
        
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-gray-900">{name}</h3>
          <p className="text-sm text-gray-600">{title}</p>
          {company && (
            <p className="text-xs text-gray-500">{company}</p>
          )}
        </div>
      </div>

      {/* Rating */}
      {rating && (
        <div className="flex items-center mb-3">
          <div className="flex items-center">
            {[...Array(5)].map((_, i) => (
              <svg
                key={i}
                className={`w-4 h-4 ${
                  i < Math.floor(rating) ? 'text-yellow-400' : 'text-gray-300'
                }`}
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
              </svg>
            ))}
            <span className="ml-2 text-sm text-gray-600">{rating}</span>
          </div>
        </div>
      )}

      {/* Specialization */}
      {specialization && specialization.length > 0 && (
        <div className="flex flex-wrap gap-1">
          {specialization.slice(0, 3).map((spec, index) => (
            <span
              key={index}
              className="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded"
            >
              {spec}
            </span>
          ))}
          {specialization.length > 3 && (
            <span className="text-xs text-gray-500">
              +{specialization.length - 3} more
            </span>
          )}
        </div>
      )}
    </div>
  )
}

export default MentorCard
