import Link from 'next/link'
import Image from 'next/image'

interface CourseCardProps {
  title: string
  category: string
  price: number
  originalPrice?: number
  trustedBy: number
  image?: string
  href: string
  isFree?: boolean
}

const CourseCard = ({ 
  title, 
  category, 
  price, 
  originalPrice, 
  trustedBy, 
  image, 
  href, 
  isFree = false 
}: CourseCardProps) => {
  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
      {/* Course Image */}
      <div className="relative h-48 bg-gray-200">
        {image ? (
          <Image
            src={image}
            alt={title}
            fill
            className="object-cover"
          />
        ) : (
          <div className="flex items-center justify-center h-full bg-gradient-to-br from-blue-500 to-purple-600">
            <span className="text-white text-lg font-semibold">{category}</span>
          </div>
        )}
        
        {/* Category Badge */}
        <div className="absolute top-3 left-3">
          <span className="bg-blue-600 text-white text-xs font-medium px-2 py-1 rounded">
            {category}
          </span>
        </div>
      </div>

      {/* Course Content */}
      <div className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
          {title}
        </h3>

        {/* Trusted By */}
        <p className="text-sm text-gray-600 mb-4">
          Trusted by {trustedBy.toLocaleString()}+
        </p>

        {/* Pricing */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {isFree ? (
              <span className="text-2xl font-bold text-green-600">₹Free</span>
            ) : (
              <>
                <span className="text-2xl font-bold text-gray-900">₹{price.toLocaleString()}</span>
                {originalPrice && originalPrice > price && (
                  <span className="text-lg text-gray-500 line-through">₹{originalPrice.toLocaleString()}</span>
                )}
              </>
            )}
          </div>
          
          <Link
            href={href}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
          >
            Learn more
          </Link>
        </div>
      </div>
    </div>
  )
}

export default CourseCard
