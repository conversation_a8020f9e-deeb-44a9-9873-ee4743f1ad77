import Link from "next/link";
import CourseCard from "@/components/CourseCard";
import MentorCard from "@/components/MentorCard";
import TestimonialCard from "@/components/TestimonialCard";

export default function Home() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 to-indigo-100 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
              Skyrocket Your<br />
              <span className="text-blue-600">Finance Career</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
              Learn industry-relevant skills from top mentors.<br />
              Network with aspirational peer group
            </p>
            <Link
              href="#scroll"
              className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-colors inline-block"
            >
              View courses
            </Link>
          </div>

          {/* Stats */}
          <div className="mt-16 grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <div>
              <div className="text-3xl font-bold text-gray-900">2.7+ L</div>
              <div className="text-gray-600">Learning Hours</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-gray-900">1:1</div>
              <div className="text-gray-600">Mentorship</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-gray-900">500+</div>
              <div className="text-gray-600">Mentors</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-gray-900">100+</div>
              <div className="text-gray-600">Companies Hiring</div>
            </div>
          </div>
        </div>
      </section>

      {/* Get Hired By Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">Get Hired By</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-8 items-center justify-items-center">
            {/* Company logos would go here */}
            {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12].map((i) => (
              <div key={i} className="w-24 h-16 bg-gray-200 rounded flex items-center justify-center">
                <span className="text-gray-500 text-sm">Logo {i}</span>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Consulting Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Consulting</h2>
            <h3 className="text-2xl font-semibold text-gray-800 mb-8">
              Achieve Your Goals<br />
              With CA MONK
            </h3>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-5 gap-8">
            {[
              'Get 1:1 Mentorship by Top 1%',
              'Network with aspirational Peer Group',
              'Get Interview Ready',
              'Undertake Structured Courses by Experts',
              'Crack your Dream Job Role'
            ].map((item, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-white font-bold text-xl">{index + 1}</span>
                </div>
                <h4 className="font-semibold text-gray-900">{item}</h4>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Course Categories Section */}
      <section id="scroll" className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Explore Courses By Categories</h2>
            <p className="text-lg text-gray-600">Discover Courses Aligned With Your Interests</p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                title: 'Interview Prep',
                description: 'Cracking interview made easy',
                icon: '🎯',
                href: '/interview-prep'
              },
              {
                title: 'Masterclasses',
                description: 'Build foundation of technical & soft skills',
                icon: '🎓',
                href: '/masterclass'
              },
              {
                title: 'Upskill',
                description: 'Learn tools & skills for faster growth',
                icon: '📈',
                href: '/upskill'
              },
              {
                title: 'Mentorship',
                description: 'Get 1:1 guidance from industry experts',
                icon: '👥',
                href: '/mentorship'
              },
              {
                title: 'Free Courses',
                description: 'Explore free courses from CA Monk',
                icon: '🆓',
                href: '/free-courses'
              },
              {
                title: 'Placement Drive',
                description: 'Get placed at top companies - SAMBHAV',
                icon: '🚀',
                href: '/placement-drive'
              }
            ].map((category, index) => (
              <Link key={index} href={category.href} className="group">
                <div className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-shadow duration-300 group-hover:border-blue-500">
                  <div className="text-4xl mb-4">{category.icon}</div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">{category.title}</h3>
                  <p className="text-gray-600">{category.description}</p>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Courses Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Featured Courses</h2>
            <p className="text-lg text-gray-600">Isn't learning an investment your future deserves?</p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <CourseCard
              title="Getting Interview Ready Workshop"
              category="Interview Prep"
              price={5999}
              originalPrice={9999}
              trustedBy={12000}
              href="/courses/getting-interview-ready-workshop"
            />
            <CourseCard
              title="Financial Modeling Workshop"
              category="Upskill"
              price={1999}
              originalPrice={4999}
              trustedBy={3621}
              href="/courses/financial-modeling-workshop"
            />
            <CourseCard
              title="Microsoft Excel Mastery Workshop"
              category="Upskill"
              price={599}
              originalPrice={4999}
              trustedBy={5587}
              href="/courses/excel-mastery-workshop"
            />
          </div>
        </div>
      </section>

      {/* Mentors Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Meet Your Mentors</h2>
            <p className="text-lg text-gray-600 mb-8">Not just any Mentor, You get a CA Monk Mentor</p>
            <Link
              href="/mentorship"
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors"
            >
              All Mentors
            </Link>
          </div>

          {/* Scrolling Mentors */}
          <div className="overflow-hidden">
            <div className="flex space-x-6 animate-scroll">
              {[
                { name: 'CA Aayush Jain', title: 'Senior Manager, Big 4', specialization: ['Audit', 'Finance'] },
                { name: 'CA Akanksha Gupta', title: 'Investment Banking Analyst', specialization: ['IB', 'Valuation'] },
                { name: 'CA Aditya V Hegde', title: 'Financial Consultant', specialization: ['Finance', 'Strategy'] },
                { name: 'CA Anjali Singhal', title: 'Tax Manager', specialization: ['Tax', 'Compliance'] },
                { name: 'CA Ameya Lokhande', title: 'Senior Auditor', specialization: ['Audit', 'Risk'] },
                { name: 'CA Diksha Kundu', title: 'Finance Manager', specialization: ['Finance', 'Analytics'] },
                { name: 'CA Anand Maheshwari', title: 'Consulting Partner', specialization: ['Consulting', 'Strategy'] },
                { name: 'CA Harleen Kaur', title: 'Senior Associate', specialization: ['Audit', 'Finance'] },
                { name: 'CA Ankit Agarwal', title: 'Investment Analyst', specialization: ['Investment', 'Research'] },
                { name: 'CA Anmol Bedi', title: 'Financial Advisor', specialization: ['Finance', 'Planning'] },
                { name: 'CA Divyanshu Negi', title: 'Tax Consultant', specialization: ['Tax', 'Advisory'] },
                { name: 'CA Ashutosh Agrawal', title: 'Audit Manager', specialization: ['Audit', 'Compliance'] },
              ].map((mentor, index) => (
                <MentorCard
                  key={index}
                  name={mentor.name}
                  title={mentor.title}
                  specialization={mentor.specialization}
                  rating={4.5}
                />
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">What Our Students Have To Say...</h2>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <TestimonialCard
              studentName="Yaksha Bora"
              testimonial="This program heightened my awareness of the essential steps required to secure a dream job. It pushed me beyond my comfort zone, encouraging me to think creatively, and ultimately, it boosted my self-confidence."
              rating={4.5}
              studentTitle="CA Student"
            />
            <TestimonialCard
              studentName="Priya Sharma"
              testimonial="The mentorship program was exceptional. My mentor guided me through every step of the interview process and helped me land my dream job at a Big 4 firm."
              rating={5}
              studentTitle="Senior Associate"
              company="Deloitte"
            />
            <TestimonialCard
              studentName="Rahul Kumar"
              testimonial="CA Monk's courses are incredibly practical and industry-relevant. The Excel mastery course alone transformed how I approach financial modeling."
              rating={4.8}
              studentTitle="Financial Analyst"
              company="Goldman Sachs"
            />
          </div>

          <div className="text-center mt-8">
            <Link
              href="/testimonials"
              className="text-blue-600 hover:text-blue-700 font-semibold"
            >
              View All
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}
