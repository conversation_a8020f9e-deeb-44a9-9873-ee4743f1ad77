@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;

  /* CA Monk Brand Colors */
  --camonk-primary: #2563eb;
  --camonk-primary-dark: #1d4ed8;
  --camonk-secondary: #10b981;
  --camonk-accent: #f59e0b;
  --camonk-gray: #6b7280;
  --camonk-gray-light: #f3f4f6;
  --camonk-gray-dark: #374151;

  /* Gradients */
  --camonk-gradient-primary: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  --camonk-gradient-secondary: linear-gradient(135deg, #10b981 0%, #059669 100%);
  --camonk-gradient-hero: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);

  /* Custom colors */
  --color-primary: var(--camonk-primary);
  --color-primary-foreground: #ffffff;
  --color-secondary: var(--camonk-secondary);
  --color-secondary-foreground: #ffffff;
  --color-muted: var(--camonk-gray-light);
  --color-muted-foreground: var(--camonk-gray);
  --color-accent: var(--camonk-accent);
  --color-accent-foreground: #ffffff;
  --color-destructive: #ef4444;
  --color-destructive-foreground: #ffffff;
  --color-border: #e5e7eb;
  --color-input: #e5e7eb;
  --color-ring: var(--camonk-primary);
  --color-card: #ffffff;
  --color-card-foreground: var(--foreground);
  --color-popover: #ffffff;
  --color-popover-foreground: var(--foreground);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Custom animations */
@keyframes scroll {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(37, 99, 235, 0.3);
  }
  50% {
    box-shadow: 0 0 30px rgba(37, 99, 235, 0.5);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.animate-scroll {
  animation: scroll 40s linear infinite;
}

.animate-scroll:hover {
  animation-play-state: paused;
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-slide-in-left {
  animation: slideInLeft 0.6s ease-out;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* Utility classes */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Glass morphism effect */
.glass {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
}

/* Gradient text */
.gradient-text {
  background: var(--camonk-gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Custom shadows */
.shadow-camonk {
  box-shadow: 0 10px 25px -5px rgba(37, 99, 235, 0.1), 0 10px 10px -5px rgba(37, 99, 235, 0.04);
}

.shadow-camonk-lg {
  box-shadow: 0 20px 25px -5px rgba(37, 99, 235, 0.1), 0 10px 10px -5px rgba(37, 99, 235, 0.04);
}

/* Hover effects */
.hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .animate-scroll {
    animation-duration: 25s;
  }

  /* Improve touch targets on mobile */
  button, a {
    min-height: 44px;
  }

  .hover-lift:hover {
    transform: none;
  }
}
